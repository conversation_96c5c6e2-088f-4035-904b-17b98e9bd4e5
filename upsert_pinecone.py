import os
import re
import json
import hashlib
from typing import List, Dict, Optional, Tuple
from dataclasses import dataclass, asdict
from pathlib import Path
from pinecone import Pinecone

NAMESPACE = "hierarchical"

pc = Pinecone(api_key=os.getenv("PINECONE_API_KEY"))
index = pc.Index(host=os.getenv("PINECONE_INDEX_CHEM"))

@dataclass
class LearningOutcome:
    """Represents a learning outcome from chemistry notes"""
    id: str
    content: str
    chapter: str
    chapter_slug: str

@dataclass
class ContentSection:
    """Represents a heading/subheading with its associated learning outcome"""
    id: str
    heading_content: str
    anchor_id: str
    learning_outcome_id: str
    learning_outcome_content: str
    chapter: str
    chapter_slug: str
    heading_level: int
    page_number: Optional[int] = None
    parent_section_id: Optional[str] = None

class ChemistryNotesExtractor:
    """Extracts learning outcomes and content sections from chemistry markdown files"""

    def __init__(self, notes_directory: str = "chemistry_notes_markdown"):
        self.notes_directory = Path(notes_directory)
        self.learning_outcomes: List[LearningOutcome] = []
        self.content_sections: List[ContentSection] = []

    def extract_all_content(self) -> Tuple[List[LearningOutcome], List[ContentSection]]:
        """Extract content from all markdown files"""
        print("🔍 Starting content extraction from chemistry notes...")

        # Get all markdown files
        md_files = list(self.notes_directory.glob("*.md"))
        print(f"📚 Found {len(md_files)} markdown files")

        for md_file in md_files:
            if md_file.name == "chem-notes-markdown.zip":
                continue

            print(f"📖 Processing: {md_file.name}")
            self._process_markdown_file(md_file)

        print(f"✅ Extraction complete: {len(self.learning_outcomes)} learning outcomes, {len(self.content_sections)} content sections")
        return self.learning_outcomes, self.content_sections

    def _process_markdown_file(self, file_path: Path):
        """Process a single markdown file"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()

            # Extract chapter info
            chapter_info = self._extract_chapter_info(file_path)

            # Extract learning outcomes
            learning_outcomes = self._extract_learning_outcomes(content, chapter_info)
            self.learning_outcomes.extend(learning_outcomes)

            # Extract headings and map to learning outcomes
            content_sections = self._extract_content_sections(content, chapter_info, learning_outcomes)
            self.content_sections.extend(content_sections)

        except Exception as e:
            print(f"❌ Error processing {file_path.name}: {e}")

    def _extract_chapter_info(self, file_path: Path) -> Dict[str, str]:
        """Extract chapter information from filename"""
        filename = file_path.stem

        # Extract chapter number and title
        # Examples: "3 Chemical Bonding I", "15 Acid-Base Equilibria", "1a Mole Concept and Stoichiometry"
        match = re.match(r'^(\d+[a-z]?)\s+(.+)$', filename)
        if match:
            chapter_number = match.group(1)
            title = match.group(2)
        else:
            chapter_number = ""
            title = filename

        # Create slug
        slug = re.sub(r'[^a-zA-Z0-9\s-]', '', title.lower())
        slug = re.sub(r'\s+', '-', slug.strip())

        return {
            'number': chapter_number,
            'title': title,
            'slug': slug,
            'filename': filename
        }

    def _extract_learning_outcomes(self, content: str, chapter_info: Dict[str, str]) -> List[LearningOutcome]:
        """Extract learning outcomes from markdown content"""
        learning_outcomes = []
        lines = content.split('\n')

        # Find learning outcomes section
        in_learning_outcomes = False
        current_outcome = ""
        outcome_counter = 1

        for i, line in enumerate(lines):
            line = line.strip()

            # Check if we're entering learning outcomes section
            if re.match(r'^##?\s*(B\s+)?Learning Outcomes?', line, re.IGNORECASE):
                in_learning_outcomes = True
                continue

            # Check if we're leaving learning outcomes section
            if in_learning_outcomes and re.match(r'^##?\s+', line) and not re.match(r'^##?\s*(B\s+)?Learning Outcomes?', line, re.IGNORECASE):
                # Save last outcome if exists
                if current_outcome.strip():
                    outcome_id = f"{chapter_info['slug']}-lo-{outcome_counter}"
                    learning_outcomes.append(LearningOutcome(
                        id=outcome_id,
                        content=current_outcome.strip(),
                        chapter=chapter_info['title'],
                        chapter_slug=chapter_info['slug']
                    ))
                break

            if in_learning_outcomes:
                # Skip empty lines and "Candidates should be able to:"
                if not line or "candidates should be able to" in line.lower():
                    continue

                # Check if this is a new outcome (starts with letter in parentheses)
                if re.match(r'^\([a-z]\)', line):
                    # Save previous outcome if exists
                    if current_outcome.strip():
                        outcome_id = f"{chapter_info['slug']}-lo-{outcome_counter}"
                        learning_outcomes.append(LearningOutcome(
                            id=outcome_id,
                            content=current_outcome.strip(),
                            chapter=chapter_info['title'],
                            chapter_slug=chapter_info['slug']
                        ))
                        outcome_counter += 1

                    # Start new outcome
                    current_outcome = line
                else:
                    # Continue current outcome
                    if current_outcome:
                        current_outcome += " " + line

        # Save final outcome if exists
        if current_outcome.strip():
            outcome_id = f"{chapter_info['slug']}-lo-{outcome_counter}"
            learning_outcomes.append(LearningOutcome(
                id=outcome_id,
                content=current_outcome.strip(),
                chapter=chapter_info['title'],
                chapter_slug=chapter_info['slug']
            ))

        return learning_outcomes

    def _extract_content_sections(self, content: str, chapter_info: Dict[str, str], learning_outcomes: List[LearningOutcome]) -> List[ContentSection]:
        """Extract headings and subheadings and map them to learning outcomes"""
        content_sections = []
        lines = content.split('\n')

        section_counter = 1
        current_page = None

        for line_num, line in enumerate(lines):
            line = line.strip()

            # Track page numbers
            if re.match(r'^##\s+Page\s+\d+', line):
                page_match = re.search(r'Page\s+(\d+)', line)
                if page_match:
                    current_page = int(page_match.group(1))
                continue

            # Check for headings (# ## ### #### ##### ######)
            heading_match = re.match(r'^(#{1,6})\s+(.+)$', line)
            if heading_match:
                heading_level = len(heading_match.group(1))
                heading_text = heading_match.group(2).strip()

                # Skip certain headings
                if self._should_skip_heading(heading_text):
                    continue

                # Generate anchor ID
                anchor_id = self._generate_anchor_id(heading_text, chapter_info['slug'], section_counter)

                # Find best matching learning outcome
                best_outcome = self._find_best_learning_outcome(heading_text, learning_outcomes)

                # Create content section
                section_id = f"{chapter_info['slug']}-section-{section_counter}"
                content_section = ContentSection(
                    id=section_id,
                    heading_content=heading_text,
                    anchor_id=anchor_id,
                    learning_outcome_id=best_outcome.id if best_outcome else "",
                    learning_outcome_content=best_outcome.content if best_outcome else "",
                    chapter=chapter_info['title'],
                    chapter_slug=chapter_info['slug'],
                    heading_level=heading_level,
                    page_number=current_page
                )

                content_sections.append(content_section)
                section_counter += 1

        return content_sections

    def _should_skip_heading(self, heading_text: str) -> bool:
        """Check if heading should be skipped"""
        skip_patterns = [
            r'^Page\s+\d+',
            r'^Learning Outcomes?',
            r'^B\s+Learning Outcomes?',
            r'^Contents?$',
            r'^References?$',
            r'^Website$',
            r'^Lecture Outline$',
            r'^Overarching Question',
            r'^\d+\.\d+\.\d+',  # Skip sub-sub-section numbers
        ]

        for pattern in skip_patterns:
            if re.match(pattern, heading_text, re.IGNORECASE):
                return True
        return False

    def _generate_anchor_id(self, heading_text: str, chapter_slug: str, counter: int) -> str:
        """Generate a clean anchor ID for the heading"""
        # Clean the heading text
        clean_text = re.sub(r'[^\w\s-]', '', heading_text.lower())
        clean_text = re.sub(r'\s+', '-', clean_text.strip())
        clean_text = re.sub(r'-+', '-', clean_text)
        clean_text = clean_text.strip('-')

        # Limit length and add counter for uniqueness
        if len(clean_text) > 50:
            clean_text = clean_text[:50].rstrip('-')

        return f"{chapter_slug}-{clean_text}-{counter}"

    def _find_best_learning_outcome(self, heading_text: str, learning_outcomes: List[LearningOutcome]) -> Optional[LearningOutcome]:
        """Find the best matching learning outcome for a heading using enhanced matching"""
        if not learning_outcomes:
            return None

        heading_lower = heading_text.lower()

        # Enhanced keyword mapping for chemistry topics
        topic_keywords = {
            'bonding': ['bond', 'ionic', 'covalent', 'metallic', 'intermolecular', 'hydrogen bonding'],
            'structure': ['structure', 'shape', 'geometry', 'vsepr', 'molecular', 'lattice'],
            'kinetics': ['rate', 'kinetic', 'catalyst', 'activation', 'mechanism', 'order'],
            'equilibrium': ['equilibrium', 'equilibria', 'constant', 'le chatelier', 'position'],
            'acids_bases': ['acid', 'base', 'ph', 'buffer', 'titration', 'indicator'],
            'organic': ['organic', 'alkane', 'alkene', 'alcohol', 'carbonyl', 'carboxylic', 'amine'],
            'energetics': ['energy', 'enthalpy', 'entropy', 'gibbs', 'thermodynamic', 'heat'],
            'electrochemistry': ['electrode', 'cell', 'potential', 'electrolysis', 'redox'],
            'periodic': ['periodic', 'group', 'period', 'trend', 'ionization', 'electronegativity']
        }

        # Find topic category for heading
        heading_category = None
        for category, keywords in topic_keywords.items():
            if any(keyword in heading_lower for keyword in keywords):
                heading_category = category
                break

        best_match = None
        best_score = 0

        for outcome in learning_outcomes:
            outcome_lower = outcome.content.lower()
            score = 0

            # Category matching bonus
            if heading_category:
                category_keywords = topic_keywords[heading_category]
                if any(keyword in outcome_lower for keyword in category_keywords):
                    score += 0.5

            # Direct keyword matching
            heading_terms = set(re.findall(r'\b\w{3,}\b', heading_lower))  # Only words 3+ chars
            outcome_terms = set(re.findall(r'\b\w{3,}\b', outcome_lower))

            common_terms = heading_terms.intersection(outcome_terms)
            if common_terms:
                overlap_score = len(common_terms) / len(heading_terms.union(outcome_terms))
                score += overlap_score

            # Specific term matching with higher weights
            important_terms = ['bond', 'structure', 'rate', 'equilibrium', 'acid', 'base', 'organic']
            for term in important_terms:
                if term in heading_lower and term in outcome_lower:
                    score += 0.3

            if score > best_score:
                best_score = score
                best_match = outcome

        # Return best match if score is reasonable, otherwise return first outcome
        return best_match if best_score > 0.2 else learning_outcomes[0] if learning_outcomes else None

    def save_structured_data(self, filename: str = "chemistry_notes_structured_data.json"):
        """Save extracted data to JSON file for inspection and backup"""
        data = {
            'learning_outcomes': [asdict(lo) for lo in self.learning_outcomes],
            'content_sections': [asdict(cs) for cs in self.content_sections],
            'extraction_stats': {
                'total_learning_outcomes': len(self.learning_outcomes),
                'total_content_sections': len(self.content_sections),
                'chapters_processed': len(set(cs.chapter for cs in self.content_sections))
            }
        }

        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(data, f, indent=2, ensure_ascii=False)

        print(f"💾 Structured data saved to {filename}")

def upsert_to_pinecone(content_sections: List[ContentSection], batch_size: int = 100):
    """Upsert content sections to Pinecone with hierarchical namespace"""
    print(f"🚀 Upserting {len(content_sections)} content sections to Pinecone...")

    # Process in batches
    for i in range(0, len(content_sections), batch_size):
        batch = content_sections[i:i + batch_size]
        records = []

        for section in batch:
            # Create text for embedding (combine heading and learning outcome)
            text_content = f"{section.heading_content}. {section.learning_outcome_content}"

            # Create metadata
            metadata = {
                "heading_content": section.heading_content,
                "anchor_id": section.anchor_id,
                "learning_outcome": section.learning_outcome_content,
                "chapter": section.chapter,
                "chapter_slug": section.chapter_slug,
                "heading_level": section.heading_level,
                "page_number": section.page_number or 0,
                "url": f"notes/{section.chapter_slug}#{section.anchor_id}"
            }

            # Pinecone will handle embedding generation automatically
            records.append({
                "id": section.id,
                "text": text_content,
                "metadata": metadata
            })

        if records:
            try:
                index.upsert(records=records, namespace=NAMESPACE)
                print(f"✅ Upserted batch {i//batch_size + 1}: {len(records)} records")
            except Exception as e:
                print(f"❌ Error upserting batch {i//batch_size + 1}: {e}")

    print(f"🎉 Upsert complete!")

def main():
    """Main function to extract content and upsert to Pinecone"""
    print("🧪 Chemistry Notes Hierarchical Content Extraction and Upsert")
    print("=" * 60)

    # Extract content
    extractor = ChemistryNotesExtractor()
    learning_outcomes, content_sections = extractor.extract_all_content()

    # Save structured data for inspection and backup
    extractor.save_structured_data()

    # Save extracted data for inspection
    print(f"\n📊 Extraction Summary:")
    print(f"   Learning Outcomes: {len(learning_outcomes)}")
    print(f"   Content Sections: {len(content_sections)}")

    # Show sample mappings
    print(f"\n🔗 Sample Learning Outcome Mappings:")
    for i, section in enumerate(content_sections[:5], 1):
        print(f"   {i}. '{section.heading_content}' → '{section.learning_outcome_content[:60]}...'")

    # Upsert to Pinecone
    if content_sections:
        upsert_to_pinecone(content_sections)
    else:
        print("❌ No content sections to upsert")

if __name__ == "__main__":
    main()

