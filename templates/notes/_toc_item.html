<!-- Table of Contents Item Template -->
<div class="toc-item">
    <a href="#{{ item.section.section_id }}" 
       class="block py-2 px-3 text-sm text-gray-700 hover:text-blue-600 hover:bg-blue-50 rounded-md transition-colors
              {% if item.section.heading_level == 1 %}font-semibold{% endif %}
              {% if item.section.heading_level == 2 %}font-medium{% endif %}
              {% if item.section.heading_level >= 3 %}ml-{{ (item.section.heading_level - 2) * 4 }}{% endif %}">
        
        <!-- Section Title -->
        <div class="flex items-center justify-between">
            <span class="flex-1 truncate">
                {% if item.section.heading_level == 1 %}
                    <i class="fas fa-bookmark text-blue-500 mr-2"></i>
                {% elif item.section.heading_level == 2 %}
                    <i class="fas fa-file-alt text-gray-400 mr-2"></i>
                {% else %}
                    <i class="fas fa-minus text-gray-300 mr-2"></i>
                {% endif %}
                {{ item.section.title }}
            </span>
            
            <!-- Page Number -->
            {% if item.section.page_number %}
            <span class="text-xs text-gray-500 ml-2">
                p{{ item.section.page_number }}
            </span>
            {% endif %}
        </div>
    </a>
    
    <!-- Nested Children -->
    {% if item.children %}
    <div class="ml-4 mt-1 space-y-1">
        {% for child in item.children %}
            {% set item = child %}
            {% include 'notes/_toc_item.html' %}
        {% endfor %}
    </div>
    {% endif %}
</div>
