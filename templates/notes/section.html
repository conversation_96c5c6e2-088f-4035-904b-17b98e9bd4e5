{% extends "base.html" %}

{% block title %}{{ section.title }} - {{ chapter.title }} - Chemistry Notes - VAST{% endblock %}

{% block content %}
<div class="min-h-screen bg-gray-50">
    <!-- Header -->
    <div class="bg-white shadow-sm border-b">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
            <nav class="flex items-center space-x-2 text-sm text-gray-500 mb-2">
                <a href="{{ url_for('notes_index') }}" class="hover:text-blue-600">
                    <i class="fas fa-book-open mr-1"></i>Chemistry Notes
                </a>
                <i class="fas fa-chevron-right text-xs"></i>
                <a href="{{ url_for('view_chapter', chapter_slug=chapter.slug) }}" class="hover:text-blue-600">
                    {{ chapter.title }}
                </a>
                <i class="fas fa-chevron-right text-xs"></i>
                <span class="text-gray-900">{{ section.title }}</span>
            </nav>
            <h1 class="text-3xl font-bold text-gray-900">{{ section.title }}</h1>
            <div class="flex items-center space-x-4 mt-2">
                {% if section.page_number %}
                <span class="text-sm text-gray-500 bg-gray-100 px-2 py-1 rounded">
                    Page {{ section.page_number }}
                </span>
                {% endif %}
                <span class="text-sm text-gray-500">
                    h{{ section.heading_level }} heading
                </span>
            </div>
        </div>
    </div>

    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div class="flex gap-8">
            <!-- Sidebar Navigation -->
            <div class="w-80 flex-shrink-0">
                <div class="bg-white rounded-lg shadow-sm border sticky top-8">
                    <div class="p-4 border-b border-gray-200">
                        <h3 class="font-semibold text-gray-900">Chapter Sections</h3>
                    </div>
                    <div class="p-4 max-h-[70vh] overflow-y-auto">
                        <nav class="space-y-1">
                            {% for item in section_hierarchy %}
                                {% include 'notes/_toc_item.html' %}
                            {% endfor %}
                        </nav>
                    </div>
                </div>
            </div>

            <!-- Main Content -->
            <div class="flex-1 min-w-0">
                <div class="bg-white rounded-lg shadow-sm border">
                    <div class="p-8">
                        <!-- Section Content -->
                        <div class="prose prose-lg max-w-none">
                            <div id="{{ section.section_id }}" class="section-content">
                                {% if section.content %}
                                <div class="section-body mb-6">
                                    {{ section.content | markdown | safe }}
                                </div>
                                {% endif %}

                                <!-- Content Blocks -->
                                {% for block in section.content_blocks %}
                                <div class="content-block mb-4">
                                    {% if block.content_type == 'text' %}
                                        {{ block.content_data | markdown | safe }}
                                    {% elif block.content_type == 'image' %}
                                        <div class="text-center my-6">
                                            <img src="{{ url_for('serve_chemistry_notes_image', filename=block.meta_data.src) }}" 
                                                 alt="{{ block.meta_data.alt_text }}" 
                                                 class="max-w-full h-auto rounded-lg shadow-sm">
                                            {% if block.meta_data.alt_text %}
                                            <p class="text-sm text-gray-600 mt-2">{{ block.meta_data.alt_text }}</p>
                                            {% endif %}
                                        </div>
                                    {% elif block.content_type == 'equation' %}
                                        <div class="equation-block my-4 text-center">
                                            {{ block.content_data | safe }}
                                        </div>
                                    {% elif block.content_type == 'table' %}
                                        <div class="table-block my-4 overflow-x-auto">
                                            {{ block.content_data | markdown | safe }}
                                        </div>
                                    {% endif %}
                                </div>
                                {% endfor %}
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Navigation Footer -->
                <div class="mt-8 flex justify-between items-center">
                    <div class="flex space-x-4">
                        {% if prev_section %}
                        <a href="{{ url_for('view_section', chapter_slug=chapter.slug, section_id=prev_section.section_id) }}" 
                           class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors">
                            <i class="fas fa-arrow-left mr-2"></i>
                            {{ prev_section.title[:30] }}{% if prev_section.title|length > 30 %}...{% endif %}
                        </a>
                        {% endif %}
                        
                        <a href="{{ url_for('view_chapter', chapter_slug=chapter.slug) }}" 
                           class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors">
                            <i class="fas fa-list mr-2"></i>
                            All Sections
                        </a>
                    </div>
                    
                    {% if next_section %}
                    <a href="{{ url_for('view_section', chapter_slug=chapter.slug, section_id=next_section.section_id) }}" 
                       class="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                        {{ next_section.title[:30] }}{% if next_section.title|length > 30 %}...{% endif %}
                        <i class="fas fa-arrow-right ml-2"></i>
                    </a>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function copyLink(sectionId) {
    const url = `${window.location.origin}{{ url_for('view_section', chapter_slug=chapter.slug, section_id='SECTION_ID') }}`.replace('SECTION_ID', sectionId);
    navigator.clipboard.writeText(url).then(() => {
        // Show a brief success message
        const button = event.target.closest('button');
        const icon = button.querySelector('i');
        icon.className = 'fas fa-check text-sm text-green-600';
        setTimeout(() => {
            icon.className = 'fas fa-link text-sm';
        }, 2000);
    });
}

// Render LaTeX in the chemistry notes content
document.addEventListener('DOMContentLoaded', function() {
    // Wait a bit for content to be fully loaded
    setTimeout(function() {
        if (typeof renderMathInElement !== 'undefined') {
            console.log('Rendering LaTeX in chemistry notes section...');
            renderMathInElement(document.body, {
                delimiters: [
                    {left: '$$', right: '$$', display: true},
                    {left: '$', right: '$', display: false},
                    {left: '\\(', right: '\\)', display: false},
                    {left: '\\[', right: '\\]', display: true}
                ],
                throwOnError: false,
                output: 'html',
                trust: true
            });
            console.log('LaTeX rendering completed for chemistry notes section');
        } else {
            console.warn('KaTeX auto-render not available');
        }
    }, 100);
});

// Smooth scrolling for TOC links
document.querySelectorAll('a[href^="#"]').forEach(anchor => {
    anchor.addEventListener('click', function (e) {
        e.preventDefault();
        const target = document.querySelector(this.getAttribute('href'));
        if (target) {
            target.scrollIntoView({ behavior: 'smooth', block: 'start' });
        }
    });
});
</script>

<style>
.prose h1, .prose h2, .prose h3, .prose h4, .prose h5, .prose h6 {
    scroll-margin-top: 2rem;
}

.section-content {
    border-left: 3px solid transparent;
    padding-left: 1rem;
    transition: all 0.3s ease;
}

.section-content:hover {
    border-left-color: #3b82f6;
    background-color: #f8fafc;
}

/* Enhanced table styling for chemistry content */
table {
    font-size: 0.9rem;
    line-height: 1.4;
}

table th {
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    font-weight: 600;
    text-align: center;
    vertical-align: middle;
    min-width: 120px;
}

table td {
    text-align: center;
    vertical-align: middle;
    min-height: 40px;
    position: relative;
}

/* Special styling for chemical formulas in tables */
table .katex {
    font-size: 0.9em;
}

table .katex-display {
    margin: 0.2em 0;
}

/* Responsive table wrapper */
.table-wrapper {
    overflow-x: auto;
    margin: 1.5rem 0;
    border-radius: 8px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

/* Alternating row colors for better readability */
table tbody tr:nth-child(even) {
    background-color: #f8fafc;
}

table tbody tr:hover {
    background-color: #e2e8f0;
    transition: background-color 0.2s ease;
}

@media (max-width: 1024px) {
    .w-80 {
        display: none;
    }

    /* Make tables more mobile-friendly */
    table {
        font-size: 0.8rem;
    }

    table th, table td {
        padding: 8px 4px;
        min-width: 80px;
    }
}
</style>
{% endblock %}
